import { reactRouter } from '@react-router/dev/vite';
import tailwindcss from '@tailwindcss/vite';
import { defineConfig } from 'vite';
import tsconfigPaths from 'vite-tsconfig-paths';
import svgr from 'vite-plugin-svgr';
export default defineConfig({
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false,
        // changeOrigin: true,
        // secure: false,
        // rewrite: path => path.replace(/^\/api/, ''),
      },
      '/auth': {
        target: 'http://localhost:8080', // Backend server
        changeOrigin: true, // Needed for virtual hosted sites
        secure: false, // Set to false if the backend uses a self-signed certificate
      },
      '/logout': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: true,
      },
      '^/messages01/.*': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false,
        ws: true,
      },
    },
  },
  plugins: [
    tailwindcss(),
    reactRouter(),
    tsconfigPaths(),
    svgr({
      svgrOptions: {
        icon: true,
      },
    }),
  ],
});
