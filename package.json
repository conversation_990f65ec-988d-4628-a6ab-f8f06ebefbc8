{"name": "real-admin-ui", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@ant-design/icons": "^6.0.0", "@ant-design/pro-components": "^2.8.7", "@ant-design/pro-layout": "^7.22.4", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@react-router/node": "^7.5.3", "@react-router/serve": "^7.5.3", "antd": "^5.24.9", "axios": "^1.9.0", "i18next": "^25.0.2", "i18next-browser-languagedetector": "^8.0.5", "i18next-fetch-backend": "^6.0.0", "isbot": "^5.1.27", "pretty-cache-header": "^1.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.5.1", "react-router": "^7.5.3", "remix-i18next": "^7.2.0", "zod": "^3.24.3"}, "devDependencies": {"@react-router/dev": "^7.5.3", "@tailwindcss/vite": "^4.1.4", "@types/node": "^22.15.3", "@types/react": "^18.2.63", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "react-router-devtools": "^1.1.10", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.4"}}